@echo off
setlocal enabledelayedexpansion

:: Enhanced Telegram Group Creator - Batch Launcher
:: Automatic dependency management and script execution
:: =====================================================

title Telegram Group Creator - Dependency Manager

:: Color codes for better visual output
:: Note: Windows batch doesn't support colors natively, but we'll use echo formatting

echo.
echo ========================================================
echo    Enhanced Telegram Group Creator - Launcher
echo ========================================================
echo.

:: Check if Python is installed
echo [INFO] Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH!
    echo [INFO] Please install Python from https://python.org
    echo [INFO] Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

:: Get Python version for display
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo [SUCCESS] Python %PYTHON_VERSION% found

:: Check if pip is available
echo [INFO] Checking pip availability...
python -m pip --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] pip is not available!
    echo [INFO] Please ensure pip is installed with Python
    pause
    exit /b 1
)
echo [SUCCESS] pip is available

:: Display system information
echo.
echo ========================================================
echo    System Information
echo ========================================================
echo.

:: Get pip version
for /f "tokens=2" %%i in ('python -m pip --version 2^>^&1') do set PIP_VERSION=%%i
echo [INFO] pip version: %PIP_VERSION%

:: Check internet connectivity
echo [INFO] Checking internet connectivity...
ping -n 1 pypi.org >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Cannot reach pypi.org - internet connection may be limited
    echo [INFO] Package installation may fail if packages are not cached
) else (
    echo [SUCCESS] Internet connectivity confirmed
)

echo.
echo ========================================================
echo    Checking Dependencies
echo ========================================================
echo.

:: Function to check if a package is installed
set "MISSING_PACKAGES="
set "PACKAGES_TO_CHECK=pyrogram colorama"

:: Check if requirements.txt exists for reference
if exist "requirements.txt" (
    echo [INFO] Found requirements.txt file
    echo [INFO] Required packages: pyrogram, colorama
) else (
    echo [INFO] No requirements.txt found, checking standard packages
)

for %%p in (%PACKAGES_TO_CHECK%) do (
    echo [INFO] Checking %%p...
    python -c "import %%p" >nul 2>&1
    if errorlevel 1 (
        echo [WARNING] %%p is not installed
        set "MISSING_PACKAGES=!MISSING_PACKAGES! %%p"
    ) else (
        :: Get package version if possible
        for /f "delims=" %%v in ('python -c "import %%p; print(getattr(%%p, '__version__', 'unknown'))" 2^>nul') do (
            echo [SUCCESS] %%p %%v is already installed
        )
    )
)

:: Install missing packages if any
if not "!MISSING_PACKAGES!"=="" (
    echo.
    echo ========================================================
    echo    Installing Missing Dependencies
    echo ========================================================
    echo.

    :: Upgrade pip first to ensure compatibility
    echo [INFO] Upgrading pip to latest version...
    python -m pip install --upgrade pip >nul 2>&1
    if errorlevel 1 (
        echo [WARNING] Could not upgrade pip, continuing with current version
    ) else (
        echo [SUCCESS] pip upgraded successfully
    )

    echo.
    for %%p in (!MISSING_PACKAGES!) do (
        echo [INFO] Installing %%p...
        echo [INFO] This may take a few moments, please wait...

        :: Install with verbose output for better user feedback
        python -m pip install %%p --no-cache-dir
        if errorlevel 1 (
            echo [ERROR] Failed to install %%p
            echo [INFO] Attempting installation with --user flag...
            python -m pip install --user %%p --no-cache-dir
            if errorlevel 1 (
                echo [ERROR] Failed to install %%p with --user flag
                echo [INFO] Please check your internet connection and try again
                echo [INFO] You may need to run as administrator
                pause
                exit /b 1
            ) else (
                echo [SUCCESS] %%p installed successfully with --user flag
            )
        ) else (
            echo [SUCCESS] %%p installed successfully
        )
        echo.
    )

    echo [SUCCESS] All missing dependencies installed successfully!
) else (
    echo.
    echo [SUCCESS] All dependencies are already installed!

    :: Check if user wants to upgrade existing packages
    echo [INFO] Would you like to upgrade existing packages to latest versions? (y/n)
    set /p "UPGRADE_CHOICE=Enter choice: "
    if /i "!UPGRADE_CHOICE!"=="y" (
        echo [INFO] Upgrading packages to latest versions...
        for %%p in (%PACKAGES_TO_CHECK%) do (
            echo [INFO] Upgrading %%p...
            python -m pip install --upgrade %%p >nul 2>&1
            if errorlevel 1 (
                echo [WARNING] Could not upgrade %%p
            ) else (
                echo [SUCCESS] %%p upgraded successfully
            )
        )
    )
)

:: Verify all packages are now available
echo.
echo ========================================================
echo    Verifying Installation
echo ========================================================
echo.

set "VERIFICATION_FAILED=0"
for %%p in (%PACKAGES_TO_CHECK%) do (
    echo [INFO] Verifying %%p...

    :: Test import and get detailed information
    python -c "import %%p; print('[SUCCESS] %%p version:', getattr(%%p, '__version__', 'unknown')); print('[INFO] %%p location:', %%p.__file__ if hasattr(%%p, '__file__') else 'built-in')" 2>nul
    if errorlevel 1 (
        echo [ERROR] %%p verification failed
        echo [INFO] Attempting to diagnose issue...

        :: Try to get more specific error information
        python -c "try: import %%p; except Exception as e: print('[ERROR] Import error:', str(e))" 2>nul
        set "VERIFICATION_FAILED=1"
    )
)

if !VERIFICATION_FAILED! equ 1 (
    echo.
    echo [ERROR] Some packages failed verification
    echo [INFO] Attempting to resolve issues...

    :: Try to reinstall failed packages
    echo [INFO] Reinstalling packages with force-reinstall...
    for %%p in (%PACKAGES_TO_CHECK%) do (
        python -c "import %%p" >nul 2>&1
        if errorlevel 1 (
            echo [INFO] Reinstalling %%p...
            python -m pip install --force-reinstall --no-deps %%p >nul 2>&1
        )
    )

    :: Final verification attempt
    echo [INFO] Final verification attempt...
    set "FINAL_VERIFICATION_FAILED=0"
    for %%p in (%PACKAGES_TO_CHECK%) do (
        python -c "import %%p" >nul 2>&1
        if errorlevel 1 (
            set "FINAL_VERIFICATION_FAILED=1"
        )
    )

    if !FINAL_VERIFICATION_FAILED! equ 1 (
        echo [ERROR] Final verification failed
        echo [INFO] Please try the following:
        echo [INFO] 1. Run this batch file as administrator
        echo [INFO] 2. Check your Python installation
        echo [INFO] 3. Manually install: pip install pyrogram colorama
        pause
        exit /b 1
    ) else (
        echo [SUCCESS] All packages verified after reinstallation
    )
)

:: Check if create.py exists
echo.
echo ========================================================
echo    Preparing to Launch Script
echo ========================================================
echo.

if not exist "create.py" (
    echo [ERROR] create.py not found in current directory!
    echo [INFO] Please ensure create.py is in the same folder as this batch file
    pause
    exit /b 1
)

echo [SUCCESS] create.py found
echo [INFO] All dependencies verified and ready
echo.

:: Countdown before launching
echo [INFO] Starting Telegram Group Creator in 3 seconds...
timeout /t 1 /nobreak >nul
echo [INFO] Starting Telegram Group Creator in 2 seconds...
timeout /t 1 /nobreak >nul
echo [INFO] Starting Telegram Group Creator in 1 second...
timeout /t 1 /nobreak >nul

echo.
echo ========================================================
echo    Launching Enhanced Telegram Group Creator
echo ========================================================
echo.

:: Create log file for this session
set "LOG_FILE=launcher_log_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%.txt"
set "LOG_FILE=%LOG_FILE: =0%"

echo [INFO] Creating session log: %LOG_FILE%
echo Telegram Group Creator Launcher Log > "%LOG_FILE%"
echo Session started: %date% %time% >> "%LOG_FILE%"
echo Python version: %PYTHON_VERSION% >> "%LOG_FILE%"
echo Dependencies verified: %PACKAGES_TO_CHECK% >> "%LOG_FILE%"
echo. >> "%LOG_FILE%"

:: Launch the Python script
echo [INFO] Launching create.py...
echo Script execution started: %date% %time% >> "%LOG_FILE%"

python create.py

:: Check if script executed successfully
set "SCRIPT_EXIT_CODE=%errorlevel%"
echo Script execution finished: %date% %time% >> "%LOG_FILE%"
echo Exit code: %SCRIPT_EXIT_CODE% >> "%LOG_FILE%"

if %SCRIPT_EXIT_CODE% neq 0 (
    echo.
    echo ========================================================
    echo    Script Execution Error
    echo ========================================================
    echo.
    echo [ERROR] The script encountered an error during execution
    echo [ERROR] Exit code: %SCRIPT_EXIT_CODE%
    echo [INFO] Please check the error messages above for details
    echo [INFO] Session log saved to: %LOG_FILE%
) else (
    echo.
    echo ========================================================
    echo    Script Completed Successfully
    echo ========================================================
    echo.
    echo [SUCCESS] Telegram Group Creator finished execution
    echo [INFO] Session log saved to: %LOG_FILE%
)

echo.
echo ========================================================
echo    Session Summary
echo ========================================================
echo.
echo [INFO] Python Version: %PYTHON_VERSION%
echo [INFO] Dependencies: All verified and ready
echo [INFO] Script Status: %if %SCRIPT_EXIT_CODE% equ 0 (echo SUCCESS) else (echo FAILED - Exit Code %SCRIPT_EXIT_CODE%)%
echo [INFO] Log File: %LOG_FILE%
echo [INFO] Session completed: %date% %time%

echo.
echo [INFO] Press any key to exit...
pause >nul
