

A comprehensive Python script for creating multiple Telegram groups with advanced features, message automation, and professional user experience.





1. **🌍 Full English Translation**
   - All Arabic text converted to English
   - User-friendly prompts and messages
   - Clear error messages and status updates

2. **💬 Advanced Message System**
   - **Message Array System**: Pre-written message collection with 10 professional messages
   - **Multiple Message Modes**:
     - Single welcome message with date
     - Single custom message
     - Multiple messages from pre-written array
     - Multiple custom messages
     - No messages option
   - **Smart Message Selection**:
     - Select specific messages by number
     - Use first N messages in order
     - Random message selection
   - **Sequential Message Sending**: Automatic delays between messages to respect rate limits
   - **Message Progress Tracking**: Real-time feedback for multi-message operations

3. **📁 Group Links Export Feature**
   - **Automatic Export**: Real-time export of group invite links to text file
   - **Session-Based Naming**: Files named using session name (e.g., "session_name_group_links.txt")
   - **Comprehensive Details**: Includes group name, ID, creation date, and invite link
   - **Real-Time Updates**: File updated as each group is created
   - **Error Handling**: Graceful handling of link generation failures

4. **📅 Enhanced Group Naming System**
   - **Date-Based Formats**:
     - Year only (e.g., "2025")
     - Year and month (e.g., "2025-01" or "January 2025")
     - Year, month, and day (e.g., "2025-01-15" or "January 15, 2025")
     - Custom name (user-defined)
   - **Flexible Styling**: Choose between numeric or text date formats
   - **Prefix/Suffix Support**: Add custom text before or after date-based names
   - **Automatic Generation**: Names generated based on current date and selected format

5. **📊 Improved Logging & Analytics**
   - Clean, essential logging only
   - Progress tracking with animated spinners
   - Comprehensive operation summary
   - Message sending statistics
   - Links export tracking
   - Better error reporting with context

6. **🎨 Visual Enhancements**
   - Colorized output for better readability
   - Different colors for different message types:
     - 🟢 Green: Success messages
     - 🔴 Red: Error messages
     - 🟡 Yellow: Warning messages
     - 🔵 Cyan: Information messages
     - 🟣 Magenta: User prompts
   - Animated spinner for progress indication
   - Professional formatting and layout

7. **👥 Session Management**
   - Custom session name input
   - Support for multiple simultaneous sessions
   - Prevents conflicts when running multiple instances
   - Default session fallback option



1. Install required dependencies:
```bash
pip install -r requirements.txt
```

2. Configure your API credentials in the script:
   - `API_ID`: Your Telegram API ID
   - `API_HASH`: Your Telegram API Hash



1. Run the script:
```bash
python create.py
```

2. Follow the interactive prompts:
   - Enter a custom session name (optional)
   - Configure group naming format and style
   - Choose message sending preferences
   - Configure message content and selection
   - Specify number of groups to create
   - Set group descriptions




- **Custom Session**: Enter a unique name for your session
- **Default Session**: Press Enter to use the default session name
- **Multiple Sessions**: Run multiple instances simultaneously


- **Year Only**: Simple year-based naming (e.g., "2025")
- **Year + Month**: Include month in name (numeric or text format)
- **Year + Month + Day**: Full date in name (numeric or text format)
- **Custom Name**: User-defined group names
- **Prefix/Suffix**: Add custom text to date-based names


- **Single Messages**:
  - Welcome message with current date
  - Custom user-defined message
- **Multiple Messages**:
  - Select from 10 pre-written professional messages
  - Create multiple custom messages
  - Choose specific messages by number
  - Use first N messages in sequence
  - Random message selection
- **Message Automation**: Automatic delays between messages for rate limit compliance


- **Automatic Export**: Real-time export to text file
- **Session-Based Naming**: Files named using session identifier
- **Comprehensive Data**: Group details, creation date, and invite links
- **Error Resilience**: Continues operation even if link generation fails


- **Group Names**: Auto-generated based on selected format or custom input
- **Group Description**: Customizable group description
- **Quantity**: Number of groups to create (unlimited)



The script includes comprehensive error handling for:
- **Rate Limiting**: Automatic waiting with countdown display and smart retry logic
- **Network Issues**: Graceful error reporting and continuation
- **Authentication Errors**: Clear error messages for login issues
- **Message Failures**: Continues group creation even if individual messages fail
- **File Operations**: Robust handling of export file creation and writing
- **Link Generation**: Graceful handling of invite link creation failures
- **Multi-Message Sending**: Individual message failure tracking and reporting



- **Real-time Progress**: Live progress indicator with animated spinner
- **Color-coded Status**: Easy-to-read status messages with professional color scheme
- **Comprehensive Summary**: Detailed reports including:
  - Group creation statistics
  - Message sending analytics
  - Links export summary
  - Overall operation status
- **Professional Formatting**: Clean, organized output with clear sections
- **Progress Tracking**: Individual progress for multi-message operations



- Python 3.7+
- pyrogram
- colorama
- Valid Telegram API credentials
- Active internet connection




The script includes 10 professionally crafted messages:
1. Welcome message with excitement
2. Community discussion announcement
3. Introduction encouragement
4. Rules and guidelines reminder
5. Community building message
6. Meaningful conversations invitation
7. Updates and announcements notice
8. Notification reminder
9. Knowledge sharing encouragement
10. Global community welcome


Group links are exported in a structured format:
```
Telegram Group Links Export
Session: your_session_name
Export Date: 2025-01-15 14:30:25
============================================================

Group Name: 2025
Group ID: -1001234567890
Creation Date: 2025-01-15 14:30:25
Invite Link: https://t.me/+AbCdEfGhIjKlMnOp
----------------------------------------
```


- **Message Delays**: 2-second delays between multiple messages
- **Flood Wait Handling**: Automatic waiting with countdown display
- **Smart Retry Logic**: Intelligent retry for rate-limited operations
- **Progress Preservation**: Maintains progress during wait periods



- **Group Type**: Creates supergroups (not basic groups) for enhanced features
- **Rate Limiting**: Automatically handled with smart waiting and retry logic
- **Multiple Sessions**: Run multiple instances simultaneously with different session names
- **Comprehensive Logging**: All operations logged with detailed status indicators
- **Backward Compatibility**: Maintains compatibility with existing functionality
- **File Safety**: Safe file operations with proper error handling
- **Memory Efficient**: Optimized for handling large numbers of groups and messages
