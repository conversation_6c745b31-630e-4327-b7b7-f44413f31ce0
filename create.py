import asyncio
import datetime
import time
import sys
import os
from pyrogram import Client, errors
from colorama import Fore, Style, init


init(autoreset=True)


DEFAULT_SESSION_NAME = "telegram_group_creator"
API_ID = 3656868
API_HASH = "3911d017831e5325cf9f02edbb3bcae1"


class Colors:
    SUCCESS = Fore.GREEN
    ERROR = Fore.RED
    WARNING = Fore.YELLOW
    INFO = Fore.CYAN
    PROMPT = Fore.MAGENTA
    RESET = Style.RESET_ALL
    BOLD = Style.BRIGHT


DEFAULT_MESSAGES = [
    "🎉 Welcome to our new group! We're excited to have you here.",
    "📢 This group was created for community discussions and updates.",
    "🌟 Feel free to introduce yourself and share your thoughts!",
    "📝 Please read the group rules and guidelines before posting.",
    "🤝 Let's build an amazing community together!",
    "💬 This is a place for meaningful conversations and networking.",
    "🎯 Stay tuned for important announcements and updates.",
    "🔔 Don't forget to turn on notifications to stay updated!",
    "📚 Share knowledge, ask questions, and help each other grow.",
    "🌍 Welcome to our global community of like-minded individuals!"
]


def get_session_name():
    """Get custom session name from user to allow multiple sessions"""
    print(f"{Colors.INFO}Session Management{Colors.RESET}")
    print("-" * 20)

    session_name = input(f"{Colors.PROMPT}Enter a custom session name (or press Enter for default): {Colors.RESET}").strip()

    if not session_name:
        session_name = DEFAULT_SESSION_NAME
        print(f"{Colors.INFO}Using default session: {session_name}{Colors.RESET}")
    else:
        print(f"{Colors.INFO}Using custom session: {session_name}{Colors.RESET}")

    return session_name

def get_group_naming_format():
    """Get group naming format preference from user"""
    print(f"\n{Colors.INFO}Group Naming System{Colors.RESET}")
    print("-" * 25)

    naming_options = {
        "1": "Year only (e.g., '2025')",
        "2": "Year and month (e.g., '2025-01' or 'January 2025')",
        "3": "Year, month, and day (e.g., '2025-01-15' or 'January 15, 2025')",
        "4": "Custom name (user-defined)"
    }

    print("Available naming formats:")
    for key, value in naming_options.items():
        print(f"  {key}. {value}")

    while True:
        choice = input(f"{Colors.PROMPT}Select naming format (1-4): {Colors.RESET}").strip()
        if choice in naming_options:
            break
        print(f"{Colors.WARNING}Please enter a number between 1 and 4.{Colors.RESET}")

    
    prefix = ""
    suffix = ""

    if choice in ["1", "2", "3"]:
        prefix = input(f"{Colors.PROMPT}Enter prefix (optional, press Enter to skip): {Colors.RESET}").strip()
        suffix = input(f"{Colors.PROMPT}Enter suffix (optional, press Enter to skip): {Colors.RESET}").strip()

        
        date_style = "numeric"
        if choice in ["2", "3"]:
            print(f"\n{Colors.INFO}Date Format Style:{Colors.RESET}")
            print("  1. Numeric (e.g., '2025-01' or '2025-01-15')")
            print("  2. Text (e.g., 'January 2025' or 'January 15, 2025')")

            while True:
                style_choice = input(f"{Colors.PROMPT}Select date style (1-2): {Colors.RESET}").strip()
                if style_choice == "1":
                    date_style = "numeric"
                    break
                elif style_choice == "2":
                    date_style = "text"
                    break
                print(f"{Colors.WARNING}Please enter 1 or 2.{Colors.RESET}")

    return choice, prefix, suffix, date_style if choice in ["2", "3"] else "numeric"

def generate_group_name(format_choice, prefix, suffix, date_style="numeric"):
    """Generate group name based on selected format"""
    now = datetime.datetime.now()

    if format_choice == "1":  
        date_part = str(now.year)
    elif format_choice == "2":  
        if date_style == "numeric":
            date_part = f"{now.year}-{now.month:02d}"
        else:
            date_part = f"{now.strftime('%B')} {now.year}"
    elif format_choice == "3":  
        if date_style == "numeric":
            date_part = f"{now.year}-{now.month:02d}-{now.day:02d}"
        else:
            date_part = f"{now.strftime('%B')} {now.day}, {now.year}"
    else:  
        return None

    
    name_parts = []
    if prefix:
        name_parts.append(prefix)
    name_parts.append(date_part)
    if suffix:
        name_parts.append(suffix)

    return " ".join(name_parts)

def get_message_preference():
    """Ask user if they want to send messages after group creation"""
    print(f"\n{Colors.INFO}Message Sending Options{Colors.RESET}")
    print("-" * 25)

    while True:
        choice = input(f"{Colors.PROMPT}Do you want to send messages after creating groups? (y/n): {Colors.RESET}").strip().lower()
        if choice in ['y', 'yes']:
            return True
        elif choice in ['n', 'no']:
            return False
        else:
            print(f"{Colors.WARNING}Please enter 'y' for yes or 'n' for no.{Colors.RESET}")

def display_message_array():
    """Display available pre-written messages"""
    print(f"\n{Colors.INFO}Available Pre-written Messages:{Colors.RESET}")
    print("-" * 40)
    for i, message in enumerate(DEFAULT_MESSAGES, 1):
        print(f"  {i:2d}. {message}")

def get_message_configuration():
    """Get comprehensive message configuration from user"""
    print(f"\n{Colors.INFO}Message Configuration System{Colors.RESET}")
    print("-" * 35)

    message_modes = {
        "1": "Single welcome message with date",
        "2": "Single custom message",
        "3": "Multiple messages from pre-written array",
        "4": "Multiple custom messages",
        "5": "No messages (group creation only)"
    }

    print("Available message modes:")
    for key, value in message_modes.items():
        print(f"  {key}. {value}")

    while True:
        choice = input(f"{Colors.PROMPT}Select message mode (1-5): {Colors.RESET}").strip()
        if choice in message_modes:
            break
        print(f"{Colors.WARNING}Please enter a number between 1 and 5.{Colors.RESET}")

    if choice == "1":
        today_date = datetime.date.today().strftime("%Y-%m-%d")
        return "single", [f"Welcome to the group! Created on {today_date}"], 1

    elif choice == "2":
        custom_message = input(f"{Colors.PROMPT}Enter your custom message: {Colors.RESET}").strip()
        message = custom_message if custom_message else "Welcome to the group!"
        return "single", [message], 1

    elif choice == "3":
        display_message_array()
        return get_array_message_selection()

    elif choice == "4":
        return get_multiple_custom_messages()

    else:  
        return "none", [], 0

def get_array_message_selection():
    """Handle selection of messages from pre-written array"""
    print(f"\n{Colors.INFO}Message Array Selection{Colors.RESET}")
    print("-" * 30)

    
    while True:
        try:
            num_messages = int(input(f"{Colors.PROMPT}How many messages do you want to send per group? (1-{len(DEFAULT_MESSAGES)}): {Colors.RESET}"))
            if 1 <= num_messages <= len(DEFAULT_MESSAGES):
                break
            print(f"{Colors.WARNING}Please enter a number between 1 and {len(DEFAULT_MESSAGES)}.{Colors.RESET}")
        except ValueError:
            print(f"{Colors.ERROR}Please enter a valid number.{Colors.RESET}")

    
    print(f"\n{Colors.INFO}Message Selection Method:{Colors.RESET}")
    print("  1. Select specific messages by number")
    print("  2. Use first N messages in order")
    print("  3. Use random selection")

    while True:
        method = input(f"{Colors.PROMPT}Select method (1-3): {Colors.RESET}").strip()
        if method in ["1", "2", "3"]:
            break
        print(f"{Colors.WARNING}Please enter 1, 2, or 3.{Colors.RESET}")

    selected_messages = []

    if method == "1":  
        print(f"\n{Colors.INFO}Select {num_messages} message(s) by entering their numbers:{Colors.RESET}")
        selected_indices = []
        for i in range(num_messages):
            while True:
                try:
                    msg_num = int(input(f"{Colors.PROMPT}Message {i+1} number (1-{len(DEFAULT_MESSAGES)}): {Colors.RESET}"))
                    if 1 <= msg_num <= len(DEFAULT_MESSAGES) and msg_num not in selected_indices:
                        selected_indices.append(msg_num)
                        selected_messages.append(DEFAULT_MESSAGES[msg_num - 1])
                        break
                    elif msg_num in selected_indices:
                        print(f"{Colors.WARNING}Message {msg_num} already selected. Choose a different one.{Colors.RESET}")
                    else:
                        print(f"{Colors.WARNING}Please enter a number between 1 and {len(DEFAULT_MESSAGES)}.{Colors.RESET}")
                except ValueError:
                    print(f"{Colors.ERROR}Please enter a valid number.{Colors.RESET}")

    elif method == "2":  
        selected_messages = DEFAULT_MESSAGES[:num_messages]

    else:  
        import random
        selected_messages = random.sample(DEFAULT_MESSAGES, num_messages)

    return "multiple", selected_messages, len(selected_messages)

def get_multiple_custom_messages():
    """Handle input of multiple custom messages"""
    print(f"\n{Colors.INFO}Multiple Custom Messages{Colors.RESET}")
    print("-" * 30)

    while True:
        try:
            num_messages = int(input(f"{Colors.PROMPT}How many custom messages do you want to create? (1-10): {Colors.RESET}"))
            if 1 <= num_messages <= 10:
                break
            print(f"{Colors.WARNING}Please enter a number between 1 and 10.{Colors.RESET}")
        except ValueError:
            print(f"{Colors.ERROR}Please enter a valid number.{Colors.RESET}")

    messages = []
    for i in range(num_messages):
        while True:
            message = input(f"{Colors.PROMPT}Enter message {i+1}: {Colors.RESET}").strip()
            if message:
                messages.append(message)
                break
            print(f"{Colors.WARNING}Message cannot be empty. Please try again.{Colors.RESET}")

    return "multiple", messages, len(messages)

def create_links_file(session_name):
    """Create and initialize the group links export file"""
    filename = f"{session_name}_group_links.txt"
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"Telegram Group Links Export\n")
            f.write(f"Session: {session_name}\n")
            f.write(f"Export Date: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 60 + "\n\n")
        return filename
    except Exception as e:
        print(f"{Colors.ERROR}❌ Error creating links file: {e}{Colors.RESET}")
        return None

def export_group_link(filename, group_name, group_id, invite_link, creation_date):
    """Export group link information to file"""
    if not filename:
        return False

    try:
        with open(filename, 'a', encoding='utf-8') as f:
            f.write(f"Group Name: {group_name}\n")
            f.write(f"Group ID: {group_id}\n")
            f.write(f"Creation Date: {creation_date}\n")
            f.write(f"Invite Link: {invite_link}\n")
            f.write("-" * 40 + "\n\n")
        return True
    except Exception as e:
        print(f"{Colors.ERROR}❌ Error writing to links file: {e}{Colors.RESET}")
        return False

async def send_multiple_messages(app, group_id, messages, group_number):
    """Send multiple messages to a group with appropriate delays"""
    sent_count = 0
    failed_count = 0

    for i, message in enumerate(messages, 1):
        try:
            await app.send_message(group_id, message)
            sent_count += 1

            
            if len(messages) > 1:
                print(f"{Colors.INFO}  📤 Message {i}/{len(messages)} sent to group {group_number}{Colors.RESET}")

            
            if i < len(messages):
                await asyncio.sleep(2)  

        except Exception as e:
            failed_count += 1
            print(f"{Colors.WARNING}  ⚠️  Message {i}/{len(messages)} failed for group {group_number}: {e}{Colors.RESET}")
            await asyncio.sleep(1)

    return sent_count, failed_count

async def main():
    print(f"{Colors.BOLD}{Colors.INFO}🚀 Enhanced Telegram Group Creator Script{Colors.RESET}")
    print(f"{Colors.INFO}{'=' * 50}{Colors.RESET}\n")

    
    session_name = get_session_name()

    
    format_choice, prefix, suffix, date_style = get_group_naming_format()

    
    send_messages = get_message_preference()
    message_mode = "none"
    messages_to_send = []
    message_count = 0

    if send_messages:
        message_mode, messages_to_send, message_count = get_message_configuration()

    print(f"\n{Colors.INFO}Initializing Telegram client...{Colors.RESET}")

    try:
        app = Client(session_name, API_ID, API_HASH)
        await app.start()
        user_info = await app.get_me()
        print(f"{Colors.SUCCESS}✅ Successfully logged in as: @{user_info.username}{Colors.RESET}\n")
    except errors.UserIsBot:
        print(f"{Colors.ERROR}❌ Error: Bots cannot be used with this script. Please use a regular user account.{Colors.RESET}")
        return
    except Exception as e:
        print(f"{Colors.ERROR}❌ Error starting client: {e}{Colors.RESET}")
        print(f"{Colors.WARNING}Please ensure Pyrogram is installed correctly and you have internet connection.{Colors.RESET}")
        return

    
    while True:
        try:
            num_groups_str = input(f"{Colors.PROMPT}Enter the number of groups to create (or 'q' to quit): {Colors.RESET}")
            if num_groups_str.lower() == 'q':
                print(f"{Colors.INFO}Operation cancelled by user.{Colors.RESET}")
                await app.stop()
                return
            num_groups = int(num_groups_str)
            if num_groups <= 0:
                print(f"{Colors.WARNING}Number must be greater than zero. Please try again.{Colors.RESET}")
                continue
            break
        except ValueError:
            print(f"{Colors.ERROR}Invalid input. Please enter a valid number.{Colors.RESET}")

    
    if format_choice == "4":  
        group_name = input(f"{Colors.PROMPT}Enter custom group name: {Colors.RESET}").strip()
        if not group_name:
            group_name = "Custom Group"
    else:
        group_name = generate_group_name(format_choice, prefix, suffix, date_style)

    
    group_description = input(f"{Colors.PROMPT}Enter group description (or press Enter for default): {Colors.RESET}").strip()
    if not group_description:
        group_description = group_name

    
    links_filename = create_links_file(session_name)
    if links_filename:
        print(f"{Colors.SUCCESS}✅ Group links will be exported to: {links_filename}{Colors.RESET}")

    print(f"\n{Colors.INFO}📋 Configuration Summary:{Colors.RESET}")
    print(f"   • Groups to create: {Colors.BOLD}{num_groups}{Colors.RESET}")
    print(f"   • Group name: {Colors.BOLD}{group_name}{Colors.RESET}")
    print(f"   • Group description: {Colors.BOLD}{group_description}{Colors.RESET}")
    print(f"   • Send messages: {Colors.BOLD}{'Yes' if send_messages else 'No'}{Colors.RESET}")
    if send_messages and message_mode != "none":
        print(f"   • Message mode: {Colors.BOLD}{message_mode}{Colors.RESET}")
        print(f"   • Messages per group: {Colors.BOLD}{message_count}{Colors.RESET}")
        if messages_to_send:
            preview = messages_to_send[0][:50] + "..." if len(messages_to_send[0]) > 50 else messages_to_send[0]
            print(f"   • First message preview: {Colors.BOLD}{preview}{Colors.RESET}")
    print(f"   • Links export: {Colors.BOLD}{'Yes' if links_filename else 'No'}{Colors.RESET}")

    print(f"\n{Colors.INFO}{'=' * 50}{Colors.RESET}")
    print(f"{Colors.INFO}🚀 Starting group creation process...{Colors.RESET}")
    print(f"{Colors.INFO}{'=' * 50}{Colors.RESET}")

    
    spinner_frames = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏']
    spinner_index = 0
    successful_groups = 0
    failed_groups = 0
    total_messages_sent = 0
    total_messages_failed = 0

    for i in range(num_groups):
        group_number = i + 1

        
        sys.stdout.write(f"\r{Colors.INFO}Creating group {group_number}/{num_groups} {spinner_frames[spinner_index]} {Colors.RESET}")
        sys.stdout.flush()
        spinner_index = (spinner_index + 1) % len(spinner_frames)

        start_time = time.time()
        try:
            
            new_group = await app.create_supergroup(
                title=group_name,
                description=group_description
            )

            elapsed_time = time.time() - start_time
            creation_date = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            
            if elapsed_time > 3:
                print(f"\r{Colors.WARNING}⚠️  Group {group_number} creation was slow ({elapsed_time:.2f}s). Continuing...{Colors.RESET}")
                await asyncio.sleep(1)

            
            invite_link = "N/A"
            try:
                invite_link_obj = await app.export_chat_invite_link(new_group.id)
                invite_link = invite_link_obj
            except Exception as link_error:
                print(f"\r{Colors.WARNING}⚠️  Could not generate invite link for group {group_number}: {link_error}{Colors.RESET}")

            
            if links_filename:
                export_group_link(links_filename, group_name, new_group.id, invite_link, creation_date)

            
            message_status = "no messages"
            if send_messages and message_mode != "none" and messages_to_send:
                try:
                    sent_count, failed_count = await send_multiple_messages(
                        app, new_group.id, messages_to_send, group_number
                    )
                    total_messages_sent += sent_count
                    total_messages_failed += failed_count

                    if failed_count == 0:
                        message_status = f"{sent_count} messages sent"
                    else:
                        message_status = f"{sent_count}/{len(messages_to_send)} messages sent"

                except Exception as msg_error:
                    print(f"\r{Colors.WARNING}⚠️  Group {group_number} created but all messages failed: {msg_error}{Colors.RESET}")
                    message_status = "all messages failed"
                    total_messages_failed += len(messages_to_send)

            successful_groups += 1
            print(f"\r{Colors.SUCCESS}✅ Group {group_number}/{num_groups} created successfully ({message_status}) - ID: {new_group.id}{Colors.RESET}")

        except errors.FloodWait as e:
            wait_time = e.value
            print(f"\r{Colors.WARNING}🚨 Telegram rate limit reached. Waiting {wait_time} seconds...{Colors.RESET}")

            
            for remaining in range(wait_time, 0, -1):
                sys.stdout.write(f"\r{Colors.WARNING}⏳ Waiting... {remaining}s remaining{Colors.RESET}")
                sys.stdout.flush()
                await asyncio.sleep(1)

            print(f"\r{Colors.INFO}✅ Wait complete. Resuming...{Colors.RESET}")
            
            i -= 1
            continue

        except Exception as e:
            failed_groups += 1
            print(f"\r{Colors.ERROR}❌ Failed to create group {group_number}/{num_groups}: {e}{Colors.RESET}")
            await asyncio.sleep(2)

    
    print(f"\n{Colors.INFO}{'=' * 60}{Colors.RESET}")
    print(f"{Colors.BOLD}{Colors.INFO}📊 Operation Summary{Colors.RESET}")
    print(f"{Colors.INFO}{'=' * 60}{Colors.RESET}")

    
    print(f"{Colors.SUCCESS}✅ Successfully created: {successful_groups} groups{Colors.RESET}")
    if failed_groups > 0:
        print(f"{Colors.ERROR}❌ Failed to create: {failed_groups} groups{Colors.RESET}")
    print(f"{Colors.INFO}📱 Total requested: {num_groups} groups{Colors.RESET}")

    
    if send_messages and message_mode != "none":
        print(f"\n{Colors.INFO}📤 Message Summary:{Colors.RESET}")
        print(f"{Colors.SUCCESS}✅ Messages sent: {total_messages_sent}{Colors.RESET}")
        if total_messages_failed > 0:
            print(f"{Colors.ERROR}❌ Messages failed: {total_messages_failed}{Colors.RESET}")
        expected_messages = successful_groups * message_count
        print(f"{Colors.INFO}📊 Expected total: {expected_messages} messages{Colors.RESET}")

    
    if links_filename:
        print(f"\n{Colors.INFO}📁 Export Summary:{Colors.RESET}")
        print(f"{Colors.SUCCESS}✅ Group links exported to: {links_filename}{Colors.RESET}")
        print(f"{Colors.INFO}📋 Links exported: {successful_groups} groups{Colors.RESET}")

    
    if successful_groups == num_groups:
        print(f"\n{Colors.SUCCESS}{Colors.BOLD}🎉 All groups created successfully!{Colors.RESET}")
    elif successful_groups > 0:
        print(f"\n{Colors.WARNING}⚠️  Partial success: {successful_groups}/{num_groups} groups created{Colors.RESET}")
    else:
        print(f"\n{Colors.ERROR}❌ No groups were created successfully{Colors.RESET}")

    print(f"\n{Colors.INFO}Closing Telegram connection...{Colors.RESET}")
    await app.stop()
    print(f"{Colors.SUCCESS}✅ Connection closed successfully.{Colors.RESET}")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print(f"\n{Colors.WARNING}⚠️  Operation cancelled by user.{Colors.RESET}")
    except Exception as e:
        print(f"{Colors.ERROR}❌ Unexpected error occurred: {e}{Colors.RESET}")
        print(f"{Colors.INFO}Please check your internet connection and try again.{Colors.RESET}")
